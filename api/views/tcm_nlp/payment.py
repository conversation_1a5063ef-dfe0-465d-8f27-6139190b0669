# -*- coding: utf-8 -*-
"""
TCM NLP 支付模块
处理支付宝支付相关功能
"""

import json
import time
import random
import traceback
import logging
from datetime import datetime
import pytz
from django.views import View
from django.http import JsonResponse, HttpResponse
from alipay import AliPay, AliPayConfig
from cryptography.hazmat.primitives import serialization
from django.db import transaction
from django.utils.decorators import method_decorator
from django_ratelimit.decorators import ratelimit

from api.models import Order, UserInfo
from .constants import ALIPAY_CONFIG

# 设置日志
logger = logging.getLogger(__name__)

# 支付宝配置
ALIPAY_APP_ID = ALIPAY_CONFIG['ALIPAY_APP_ID']
app_private_key_string = ALIPAY_CONFIG['app_private_key_string']
alipay_public_key_string = ALIPAY_CONFIG['alipay_public_key_string']

def get_client_ip(request):
    """获取客户端真实IP"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

class AlipayView(View):
    """支付宝支付视图类"""
    
    def create_alipay(self):
        """创建支付宝实例"""
        try:
            print("Creating AliPay instance...")
            alipay = AliPay(
                appid=ALIPAY_APP_ID,
                app_notify_url=None,  # 默认异步回调url
                app_private_key_string=app_private_key_string.strip(),
                alipay_public_key_string=alipay_public_key_string.strip(),
                sign_type="RSA2",
                debug=False,  # 默认False
                verbose=False,  # 输出调试数据
                config=AliPayConfig(timeout=15)  # 可选，请求超时时间
            )
            print("AliPay instance created successfully.")
            return alipay
        except Exception as e:
            print(f"Error creating AliPay instance: {e}")
            raise

    def alipay_pay(self, subject, total_amount, out_trade_no):
        """生成支付订单"""
        try:
            print("Generating payment order...")
            alipay = self.create_alipay()  # 先实例化alipay
            order_string = alipay.api_alipay_trade_app_pay(
                out_trade_no=out_trade_no,  # 商户订单号
                total_amount=total_amount,  # 支付总金额
                subject=subject,  # 订单标题
                notify_url="https://riyuetcm.com/api/tcmNLP/AlipayAuthCallbackView/"  # 异步回调url
            )
            print(f"Payment order generated successfully: {order_string}")
            return order_string  # 返回订单字符串
        except Exception as e:
            print(f"Error generating payment order: {e}")
            raise

    def post(self, request, *args, **kwargs):
        """处理支付请求"""
        try:
            print("Initiating payment process...")
            user_id = request.user_id
            print('支付系统用户id:', user_id)
            subject = "日月有数会员"
            
            # 从请求体中获取attach参数
            if hasattr(request, 'data'):  # 使用REST framework的情况
                attach = request.data.get('attach', 'monthly')
            else:  # 普通Django请求
                data = json.loads(request.body)
                attach = data.get('attach', 'monthly')
                
            print('收到的订阅类型:', attach)

            # 设置套餐金额
            amount_mapping = {
                'monthly': 0.06,
                'quarterly': 0.15,
                'yearly': 0.5
            }
            total_amount = amount_mapping.get(attach)
            
            # 参数验证
            if not total_amount:
                return JsonResponse({'error': 'Invalid membership type'}, status=400)

            out_trade_no = f"{int(time.time())}_{user_id}_{random.randint(1000,9999)}"
            
            # 创建订单
            order = Order.objects.create(
                user_id=user_id,
                order_id=out_trade_no,
                subject=subject,
                total_amount=total_amount,
                status='Unpaid',  # 设置初始状态为未支付
                attach=attach  # 订阅类型
            )
            
            # 获取订单字符串
            order_string = self.alipay_pay(subject, total_amount, out_trade_no)
            
            return JsonResponse({'order_string': order_string, 'out_trade_no': out_trade_no})

        except Exception as e:
            print(f"Error in payment process: {e}")
            return HttpResponse("Payment process failed.", status=500)


@method_decorator(ratelimit(key='ip', rate='10/m', block=True), name='post')
@method_decorator(ratelimit(key='ip', rate='30/m', block=True), name='get')
class AlipayAuthCallbackView(View):
    """支付宝回调处理视图 - 已加强安全防护"""
    
    def post(self, request, *args, **kwargs):
        """处理支付宝异步回调"""
        client_ip = get_client_ip(request)
        start_time = time.time()
        
        # 详细的安全日志记录
        security_log = {
            'event': 'alipay_callback_attempt',
            'client_ip': client_ip,
            'timestamp': datetime.now().isoformat(),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'content_type': request.META.get('CONTENT_TYPE', ''),
            'method': request.method
        }
        
        try:
            params = request.POST.dict()
            security_log['params_count'] = len(params)
            security_log['has_sign'] = 'sign' in params
            
            # 🚫 优先检查：如果IP已被自动拉黑，直接拒绝
            if self.is_ip_auto_banned(client_ip):
                logger.warning(f"拒绝已拉黑攻击IP访问: {client_ip}")
                return HttpResponse("Access denied", status=403)
            
            # 移除白名单机制 - 没必要，有自动拉黑就够了
            
            print(f"Received Alipay POST callback from {client_ip} with params: {params}")
            
            # 验证必要参数
            required_params = ['out_trade_no', 'trade_status', 'total_amount', 'sign']
            missing_params = [param for param in required_params if param not in params]
            if missing_params:
                security_log['status'] = 'missing_params'
                security_log['missing_params'] = missing_params
                logger.warning(f"支付宝回调缺少必要参数: {json.dumps(security_log, ensure_ascii=False)}")
                
                # 🚫 自动拉黑：正常回调不会缺少关键参数
                self._auto_ban_attacker_ip(client_ip, f"缺少必要参数: {missing_params}")
                
                return HttpResponse("Missing required parameters", status=400)
            
            # 创建支付宝实例用于验证签名
            alipay = AliPay(
                appid=ALIPAY_APP_ID,
                app_notify_url=None,
                app_private_key_string=app_private_key_string.strip(),
                alipay_public_key_string=alipay_public_key_string.strip(),
                sign_type="RSA2",
                debug=False,
                verbose=False,
                config=AliPayConfig(timeout=15)
            )
            
            # 提取支付宝发送的原始签名
            signature = params.pop('sign', None)
            data = params
            
            # 验证签名
            is_verified = alipay.verify(params, signature)
            if not is_verified:
                security_log['status'] = 'signature_verification_failed'
                security_log['trade_no'] = params.get('trade_no', 'unknown')
                security_log['out_trade_no'] = params.get('out_trade_no', 'unknown')
                logger.warning(f"支付宝回调签名验证失败: {json.dumps(security_log, ensure_ascii=False)}")
                print("Verification failed.")
                
                # 🚫 自动拉黑：正常支付宝回调不会验证失败，这100%是攻击者
                self._auto_ban_attacker_ip(client_ip, "签名验证失败 - 疑似攻击")
                
                return HttpResponse("Invalid signature", status=403)

            # 验证交易状态
            if params.get('trade_status') not in ['TRADE_SUCCESS', 'TRADE_FINISHED']:
                security_log['status'] = 'invalid_trade_status'
                security_log['trade_status'] = params.get('trade_status')
                logger.warning(f"支付宝回调交易状态无效: {json.dumps(security_log, ensure_ascii=False)}")
                
                # 🚫 自动拉黑：正常支付宝回调状态不会异常
                self._auto_ban_attacker_ip(client_ip, f"交易状态异常: {params.get('trade_status')}")
                
                return HttpResponse("Invalid transaction", status=403)

            # 检查交易金额和订单信息
            order_id = params.get('out_trade_no')
            
            # 验证订单号格式
            try:
                timestamp, user_id, random_num = order_id.split('_')
                user_id = int(user_id)
            except (ValueError, AttributeError):
                security_log['status'] = 'invalid_order_format'
                security_log['order_id'] = order_id
                logger.warning(f"支付宝回调订单号格式错误: {json.dumps(security_log, ensure_ascii=False)}")
                return HttpResponse("Invalid order format", status=403)

            # 查询订单
            try:
                order = Order.objects.get(order_id=order_id)
            except Order.DoesNotExist:
                security_log['status'] = 'order_not_found'
                security_log['order_id'] = order_id
                logger.warning(f"支付宝回调订单不存在: {json.dumps(security_log, ensure_ascii=False)}")
                return HttpResponse("Order not found", status=404)

            # 验证金额
            expected_amounts = {
                'monthly': '0.06',
                'quarterly': '0.15',
                'yearly': '0.50'
            }
            received_amount = params.get('total_amount')
            expected_amount = expected_amounts.get(order.attach)
            
            if received_amount != expected_amount:
                security_log['status'] = 'amount_mismatch'
                security_log['expected_amount'] = expected_amount
                security_log['received_amount'] = received_amount
                logger.error(f"支付宝回调金额不匹配: {json.dumps(security_log, ensure_ascii=False)}")
                
                # 🚫 自动拉黑：金额篡改100%是攻击行为
                self._auto_ban_attacker_ip(client_ip, f"金额篡改: 期望{expected_amount}, 收到{received_amount}")
                
                return HttpResponse("Amount verification failed", status=403)

            # 防重复处理
            if order.status == 'Paid':
                security_log['status'] = 'already_processed'
                logger.info(f"支付宝回调订单已处理: {json.dumps(security_log, ensure_ascii=False)}")
                return HttpResponse("success")

            # 使用数据库事务确保状态更新的一致性
            with transaction.atomic():
                user = UserInfo.objects.get(id=user_id)
                order.wx_openid = user.wx_openid_new
                order.wx_phone = user.wx_phone_new
                order.status = 'Completed'
                order.trade_no = params.get('trade_no')
                order.telephoneidstar = params.get('buyer_logon_id', '')
                tz = pytz.timezone('Asia/Shanghai')
                order.pay_time = datetime.now(tz)
                order.save()

                print("Verification successful.")
                
                # 在同一事务中更新用户会员状态
                user.update_membership(order.attach)
                print(f'用户 {user_id} 会员状态更新成功，类型: {order.attach}')
                
            # 成功处理日志
            security_log['status'] = 'success'
            security_log['processing_time'] = f"{(time.time() - start_time) * 1000:.2f}ms"
            logger.info(f"支付宝回调处理成功: {json.dumps(security_log, ensure_ascii=False)}")
            print("success")
            return HttpResponse("success")
            
        except Exception as e:
            security_log['status'] = 'exception'
            security_log['error'] = str(e)
            security_log['processing_time'] = f"{(time.time() - start_time) * 1000:.2f}ms"
            logger.error(f"支付宝回调处理异常: {json.dumps(security_log, ensure_ascii=False)}")
            print(f"Error handling Alipay callback: {e}")
            print(f"回调处理失败的详细信息: {traceback.format_exc()}")
            return HttpResponse("Error processing request.", status=500)
    
    def _auto_ban_attacker_ip(self, ip, reason):
        """自动拉黑攻击者IP - 存储到数据库"""
        try:
            from api.models import BannedPaymentIP
            from django.utils import timezone
            from datetime import timedelta
            
            # 检查是否已经被拉黑
            existing_ban = BannedPaymentIP.objects.filter(
                ip_address=ip, 
                is_active=True
            ).first()
            
            if existing_ban:
                # 已经拉黑，更新原因
                existing_ban.reason = f"{existing_ban.reason}; {reason}"
                existing_ban.save()
                logger.info(f"更新拉黑原因: {ip} - {reason}")
                return
            
            # 创建新的拉黑记录
            banned_until = timezone.now() + timedelta(hours=24)  # 24小时后过期
            
            BannedPaymentIP.objects.create(
                ip_address=ip,
                reason=reason,
                banned_until=banned_until,
                is_permanent=False,
                auto_banned=True,
                created_by="支付系统自动",
                notes=f"攻击支付宝回调接口，自动拉黑24小时"
            )
            
            logger.warning(f"🚫 自动拉黑攻击IP: {ip}, 原因: {reason}")
            print(f"🚫 自动拉黑攻击IP: {ip}")
            
        except Exception as e:
            logger.error(f"自动拉黑IP失败: {e}")
    
    def is_ip_auto_banned(self, ip):
        """检查IP是否被拉黑"""
        try:
            from api.models import BannedPaymentIP
            from django.utils import timezone
            
            # 查询数据库中的拉黑记录
            banned_ip = BannedPaymentIP.objects.filter(
                ip_address=ip,
                is_active=True
            ).first()
            
            if not banned_ip:
                return False
            
            # 检查是否已过期
            if banned_ip.is_expired:
                # 自动禁用过期的拉黑记录
                banned_ip.is_active = False
                banned_ip.save()
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查IP拉黑状态失败: {e}")
            return False
    
    def update_order_status(self, order_id, status):
        """更新订单状态"""
        print(f"Order {order_id} updated to {status}")
        Order.objects.filter(order_id=order_id).update(status=status)
    
    def get(self, request, *args, **kwargs):
        """处理GET请求，通常用于服务验证或配置确认"""
        try:
            client_ip = get_client_ip(request)
            logger.info(f"GET request received for AlipayAuthCallbackView from {client_ip}")
            print("GET request received for AlipayAuthCallbackView")
            return HttpResponse("Alipay callback service is running.")
        except Exception as e:
            print(f"Error handling GET request: {e}")
            return HttpResponse("Error processing request.", status=500)


class CheckPaymentStatusView(View):
    """检查支付状态视图"""

    async def post(self, request, *args, **kwargs):
        """检查支付状态"""
        try:
            data = json.loads(request.body.decode('utf-8'))
            out_trade_no = data.get('out_trade_no')
            
            if not out_trade_no:
                return JsonResponse({'error': 'Missing out_trade_no'}, status=400)
            
            try:
                from api.utils.async_utils import get_async
                order = await get_async(Order, order_id=out_trade_no)
                return JsonResponse({
                    'status': order.status,
                    'order_id': order.order_id,
                    'total_amount': str(order.total_amount),
                    'attach': order.attach
                })
            except Order.DoesNotExist:
                return JsonResponse({'error': 'Order not found'}, status=404)
                
        except Exception as e:
            print(f"Error checking payment status: {e}")
            return JsonResponse({'error': 'Internal server error'}, status=500)
    
    def get(self, request, *args, **kwargs):
        """处理GET请求"""
        print("GET request received for CheckPaymentStatusView")
        return JsonResponse({'message': 'Payment status check service is running.'}) 