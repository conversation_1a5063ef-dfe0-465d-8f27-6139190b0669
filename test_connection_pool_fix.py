#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试连接池泄露修复效果
通过大量并发请求验证连接池是否正常工作
"""

import requests
import json
import time
import threading
import concurrent.futures
from datetime import datetime

# 读取测试token
def get_test_token():
    try:
        with open('test_token_user_2.txt', 'r') as f:
            return f.read().strip()
    except FileNotFoundError:
        print("❌ 未找到测试token文件 test_token_user_2.txt")
        return None

def single_request(request_id):
    """单个请求测试"""
    token = get_test_token()
    if not token:
        return {"error": "No token", "request_id": request_id}
    
    url = "http://localhost:8000/api/async-bank/daily-quiz-score/"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 测试数据
    test_data = {
        "score": 80 + (request_id % 20),  # 变化的分数
        "time_spent": 100 + (request_id % 50),  # 变化的耗时
        "question_count": 5,
        "correct_count": 4,
        "difficulty": (request_id % 3) + 1,  # 轮换难度
        "category_id": (request_id % 5) + 1,  # 轮换分类
    }
    
    try:
        start_time = time.time()
        response = requests.post(url, headers=headers, json=test_data, timeout=10)
        end_time = time.time()
        
        return {
            "request_id": request_id,
            "status_code": response.status_code,
            "response_time": end_time - start_time,
            "success": response.status_code == 200,
            "timestamp": datetime.now().strftime('%H:%M:%S.%f')[:-3]
        }
        
    except requests.exceptions.Timeout:
        return {
            "request_id": request_id,
            "error": "timeout",
            "success": False,
            "timestamp": datetime.now().strftime('%H:%M:%S.%f')[:-3]
        }
    except Exception as e:
        return {
            "request_id": request_id,
            "error": str(e),
            "success": False,
            "timestamp": datetime.now().strftime('%H:%M:%S.%f')[:-3]
        }

def test_concurrent_requests(num_requests=20, max_workers=10):
    """并发请求测试"""
    print(f"🚀 开始并发测试: {num_requests} 个请求，{max_workers} 个并发线程")
    print(f"⏰ 开始时间: {datetime.now().strftime('%H:%M:%S')}")
    
    start_time = time.time()
    results = []
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_id = {executor.submit(single_request, i): i for i in range(num_requests)}
        
        # 收集结果
        for future in concurrent.futures.as_completed(future_to_id):
            result = future.result()
            results.append(result)
            
            # 实时显示进度
            if result.get("success"):
                print(f"✅ 请求 {result['request_id']:2d} 成功 - {result['timestamp']} - {result['response_time']:.3f}s")
            else:
                print(f"❌ 请求 {result['request_id']:2d} 失败 - {result['timestamp']} - {result.get('error', 'unknown')}")
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 统计结果
    successful = [r for r in results if r.get("success")]
    failed = [r for r in results if not r.get("success")]
    
    print(f"\n📊 测试结果统计:")
    print(f"⏱️ 总耗时: {total_time:.2f}秒")
    print(f"✅ 成功请求: {len(successful)}/{num_requests} ({len(successful)/num_requests*100:.1f}%)")
    print(f"❌ 失败请求: {len(failed)}/{num_requests} ({len(failed)/num_requests*100:.1f}%)")
    
    if successful:
        avg_response_time = sum(r["response_time"] for r in successful) / len(successful)
        max_response_time = max(r["response_time"] for r in successful)
        min_response_time = min(r["response_time"] for r in successful)
        print(f"📈 响应时间: 平均 {avg_response_time:.3f}s, 最大 {max_response_time:.3f}s, 最小 {min_response_time:.3f}s")
    
    if failed:
        print(f"💥 失败原因:")
        error_counts = {}
        for r in failed:
            error = r.get("error", "unknown")
            error_counts[error] = error_counts.get(error, 0) + 1
        for error, count in error_counts.items():
            print(f"   - {error}: {count} 次")
    
    # 判断测试是否通过
    success_rate = len(successful) / num_requests
    if success_rate >= 0.9:  # 90% 成功率
        print(f"\n🎉 测试通过! 连接池问题已修复")
        return True
    else:
        print(f"\n⚠️ 测试未通过，成功率过低: {success_rate*100:.1f}%")
        return False

def test_sequential_requests(num_requests=10):
    """顺序请求测试"""
    print(f"\n🔄 开始顺序测试: {num_requests} 个请求")
    
    results = []
    for i in range(num_requests):
        result = single_request(i)
        results.append(result)
        
        if result.get("success"):
            print(f"✅ 请求 {i+1:2d}/{num_requests} 成功 - {result['response_time']:.3f}s")
        else:
            print(f"❌ 请求 {i+1:2d}/{num_requests} 失败 - {result.get('error', 'unknown')}")
        
        time.sleep(0.1)  # 短暂间隔
    
    successful = [r for r in results if r.get("success")]
    print(f"📊 顺序测试结果: {len(successful)}/{num_requests} 成功")
    
    return len(successful) == num_requests

if __name__ == "__main__":
    print("🧪 连接池泄露修复验证测试")
    print("=" * 50)
    
    # 测试1: 顺序请求
    sequential_success = test_sequential_requests(5)
    
    # 测试2: 并发请求
    concurrent_success = test_concurrent_requests(20, 8)
    
    # 测试3: 高并发请求
    print(f"\n" + "=" * 50)
    high_concurrent_success = test_concurrent_requests(30, 15)
    
    print(f"\n" + "=" * 50)
    print(f"🏁 最终结果:")
    print(f"   顺序测试: {'✅ 通过' if sequential_success else '❌ 失败'}")
    print(f"   并发测试: {'✅ 通过' if concurrent_success else '❌ 失败'}")
    print(f"   高并发测试: {'✅ 通过' if high_concurrent_success else '❌ 失败'}")
    
    if all([sequential_success, concurrent_success, high_concurrent_success]):
        print(f"\n🎉 所有测试通过! 连接池泄露问题已成功修复!")
    else:
        print(f"\n⚠️ 部分测试失败，可能仍存在连接池问题")
